package com.xue.love.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.xue.love.R
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

/**
 * 智能悬浮导航球
 * 提供快捷页面切换和智能隐藏功能
 */
@Composable
fun SmartFloatingNav(
    currentPage: Int,
    onNavigateToHome: () -> Unit,
    onNavigateToChat: () -> Unit,
    onNavigateToProfile: () -> Unit,
    modifier: Modifier = Modifier,
    autoHide: Boolean = true,
    autoHideDelay: Long = 3000L
) {
    var isExpanded by remember { mutableStateOf(false) }
    var isVisible by remember { mutableStateOf(true) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    
    // 自动隐藏逻辑
    LaunchedEffect(isExpanded, autoHide) {
        if (autoHide && !isExpanded) {
            delay(autoHideDelay)
            isVisible = false
        }
    }
    
    // 点击时显示
    LaunchedEffect(isExpanded) {
        if (isExpanded) {
            isVisible = true
        }
    }
    
    val scale by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "nav_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.3f,
        animationSpec = tween(300),
        label = "nav_alpha"
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .zIndex(10f)
    ) {
        // 主导航球
        Box(
            modifier = Modifier
                .offset { IntOffset(offsetX.roundToInt(), offsetY.roundToInt()) }
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp)
                .scale(scale)
                .graphicsLayer { this.alpha = alpha }
                .pointerInput(Unit) {
                    detectDragGestures { change, dragAmount ->
                        offsetX += dragAmount.x
                        offsetY += dragAmount.y
                        
                        // 限制拖拽范围
                        val maxX = size.width - with(density) { 80.dp.toPx() }
                        val maxY = size.height - with(density) { 80.dp.toPx() }
                        
                        offsetX = offsetX.coerceIn(-maxX, 0f)
                        offsetY = offsetY.coerceIn(-maxY / 2, maxY / 2)
                    }
                }
        ) {
            FloatingNavBall(
                isExpanded = isExpanded,
                currentPage = currentPage,
                onToggleExpanded = { 
                    isExpanded = !isExpanded
                    isVisible = true
                },
                onNavigateToHome = {
                    onNavigateToHome()
                    isExpanded = false
                },
                onNavigateToChat = {
                    onNavigateToChat()
                    isExpanded = false
                },
                onNavigateToProfile = {
                    onNavigateToProfile()
                    isExpanded = false
                }
            )
        }
    }
}

/**
 * 悬浮导航球主体
 */
@Composable
private fun FloatingNavBall(
    isExpanded: Boolean,
    currentPage: Int,
    onToggleExpanded: () -> Unit,
    onNavigateToHome: () -> Unit,
    onNavigateToChat: () -> Unit,
    onNavigateToProfile: () -> Unit,
    modifier: Modifier = Modifier
) {
    val expandedWidth by animateDpAsState(
        targetValue = if (isExpanded) 200.dp else 56.dp,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "nav_width"
    )
    
    val expandedHeight by animateDpAsState(
        targetValue = if (isExpanded) 160.dp else 56.dp,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "nav_height"
    )
    
    Card(
        modifier = modifier
            .size(width = expandedWidth, height = expandedHeight)
            .shadow(8.dp, RoundedCornerShape(28.dp)),
        shape = RoundedCornerShape(28.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        )
    ) {
        AnimatedContent(
            targetState = isExpanded,
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) togetherWith 
                fadeOut(animationSpec = tween(300))
            },
            label = "nav_content"
        ) { expanded ->
            if (expanded) {
                ExpandedNavContent(
                    currentPage = currentPage,
                    onNavigateToHome = onNavigateToHome,
                    onNavigateToChat = onNavigateToChat,
                    onNavigateToProfile = onNavigateToProfile,
                    onCollapse = onToggleExpanded
                )
            } else {
                CollapsedNavContent(
                    currentPage = currentPage,
                    onExpand = onToggleExpanded
                )
            }
        }
    }
}

/**
 * 收起状态的导航内容
 */
@Composable
private fun CollapsedNavContent(
    currentPage: Int,
    onExpand: () -> Unit
) {
    val currentIcon = when (currentPage) {
        0 -> R.drawable.ic_home_filled
        1 -> R.drawable.ic_chat_filled
        2 -> R.drawable.ic_profile_filled
        else -> R.drawable.ic_heart_filled
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clickable { onExpand() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(currentIcon),
            contentDescription = "导航菜单",
            tint = Color(0xFFFF6B9D),
            modifier = Modifier.size(28.dp)
        )
    }
}

/**
 * 展开状态的导航内容
 */
@Composable
private fun ExpandedNavContent(
    currentPage: Int,
    onNavigateToHome: () -> Unit,
    onNavigateToChat: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onCollapse: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(12.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 标题栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "快捷导航",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
            
            IconButton(
                onClick = onCollapse,
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = "收起",
                    tint = Color(0xFFFF6B9D),
                    modifier = Modifier.size(16.dp)
                )
            }
        }
        
        // 导航按钮
        NavButton(
            icon = R.drawable.ic_home_filled,
            label = "主页",
            isSelected = currentPage == 0,
            onClick = onNavigateToHome
        )
        
        NavButton(
            icon = R.drawable.ic_chat_filled,
            label = "聊天",
            isSelected = currentPage == 1,
            onClick = onNavigateToChat
        )
        
        NavButton(
            icon = R.drawable.ic_profile_filled,
            label = "我的",
            isSelected = currentPage == 2,
            onClick = onNavigateToProfile
        )
    }
}

/**
 * 导航按钮组件
 */
@Composable
private fun NavButton(
    icon: Int,
    label: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFFFF6B9D).copy(alpha = 0.1f) else Color.Transparent,
        animationSpec = tween(200),
        label = "button_background"
    )
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 12.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(icon),
            contentDescription = label,
            tint = if (isSelected) Color(0xFFFF6B9D) else Color(0xFF666666),
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color(0xFFFF6B9D) else Color(0xFF666666),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}
