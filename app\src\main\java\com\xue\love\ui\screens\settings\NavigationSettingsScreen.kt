package com.xue.love.ui.screens.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.ui.components.NavigationPreview
import com.xue.love.ui.components.NavigationSettings
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.theme.CoupleAppTheme
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 导航设置界面
 * 允许用户自定义导航行为和体验
 */
@Composable
fun NavigationSettingsScreen(
    onBackClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 导航设置状态
    var enableSwipeGestures by remember { mutableStateOf(true) }
    var enableQuickNav by remember { mutableStateOf(true) }
    var enableFloatingNav by remember { mutableStateOf(false) }
    var autoHideFloatingNav by remember { mutableStateOf(true) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "导航设置",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color(0xFFFF6B9D).copy(alpha = 0.1f),
                    titleContentColor = Color(0xFF2D1B20),
                    navigationIconContentColor = Color(0xFF2D1B20)
                )
            )
        }
    ) { paddingValues ->
        Box(modifier = modifier.fillMaxSize()) {
            RomanticBackground(
                themeType = CoupleThemeType.SWEET_PINK
            ) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        top = paddingValues.calculateTopPadding() + 16.dp,
                        bottom = 16.dp
                    ),
                    verticalArrangement = Arrangement.spacedBy(20.dp)
                ) {
                    item {
                        // 导航设置
                        NavigationSettings(
                            enableSwipeGestures = enableSwipeGestures,
                            onSwipeGesturesChanged = { enableSwipeGestures = it },
                            enableQuickNav = enableQuickNav,
                            onQuickNavChanged = { enableQuickNav = it },
                            enableFloatingNav = enableFloatingNav,
                            onFloatingNavChanged = { enableFloatingNav = it },
                            autoHideFloatingNav = autoHideFloatingNav,
                            onAutoHideChanged = { autoHideFloatingNav = it }
                        )
                    }
                    
                    item {
                        // 预览效果
                        NavigationPreview(
                            enableSwipeGestures = enableSwipeGestures,
                            enableQuickNav = enableQuickNav,
                            enableFloatingNav = enableFloatingNav
                        )
                    }
                    
                    item {
                        // 使用说明
                        UsageInstructions(
                            enableSwipeGestures = enableSwipeGestures,
                            enableQuickNav = enableQuickNav,
                            enableFloatingNav = enableFloatingNav
                        )
                    }
                    
                    item {
                        // 重置按钮
                        ResetSettingsButton(
                            onReset = {
                                enableSwipeGestures = true
                                enableQuickNav = true
                                enableFloatingNav = false
                                autoHideFloatingNav = true
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 使用说明组件
 */
@Composable
private fun UsageInstructions(
    enableSwipeGestures: Boolean,
    enableQuickNav: Boolean,
    enableFloatingNav: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.9f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "使用说明",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
            
            if (enableSwipeGestures) {
                InstructionItem(
                    title = "手势导航",
                    description = "在任意页面左右滑动即可快速切换到相邻页面"
                )
            }
            
            if (enableQuickNav) {
                InstructionItem(
                    title = "快捷导航",
                    description = "聊天界面顶部的按钮可直接跳转到主页或个人中心"
                )
            }
            
            if (enableFloatingNav) {
                InstructionItem(
                    title = "悬浮导航球",
                    description = "点击悬浮球展开导航菜单，可拖拽到任意位置"
                )
            }
            
            if (!enableSwipeGestures && !enableQuickNav && !enableFloatingNav) {
                InstructionItem(
                    title = "底部导航",
                    description = "使用底部导航栏进行页面切换"
                )
            }
        }
    }
}

/**
 * 说明项组件
 */
@Composable
private fun InstructionItem(
    title: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "•",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFFFF6B9D),
            fontWeight = FontWeight.Bold
        )
        
        Column {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 重置设置按钮
 */
@Composable
private fun ResetSettingsButton(
    onReset: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.9f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "重置设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = "将所有导航设置恢复为默认值",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
            
            OutlinedButton(
                onClick = onReset,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color(0xFFFF6B9D)
                )
            ) {
                Text(
                    text = "恢复默认设置",
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun NavigationSettingsScreenPreview() {
    CoupleAppTheme {
        NavigationSettingsScreen()
    }
}
