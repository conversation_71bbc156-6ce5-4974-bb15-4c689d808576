package com.xue.love.ui.screens.auth

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.R
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.Pink80
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart
import kotlinx.coroutines.delay

/**
 * 应用锁界面
 * 提供密码输入和浪漫的解锁动画
 */
@Composable
fun AppLockScreen(
    onUnlockSuccess: () -> Unit,
    onBiometricAuth: () -> Unit,
    modifier: Modifier = Modifier
) {
    var enteredPassword by remember { mutableStateOf("") }
    var isError by remember { mutableStateOf(false) }
    var isUnlocking by remember { mutableStateOf(false) }
    
    // 处理解锁逻辑
    LaunchedEffect(isUnlocking) {
        if (isUnlocking) {
            delay(1000)
            onUnlockSuccess()
        }
    }
    
    // 处理错误状态
    LaunchedEffect(isError) {
        if (isError) {
            delay(1000)
            enteredPassword = ""
            isError = false
        }
    }
    
    // 正确密码（实际应用中应该从安全存储中获取）
    val correctPassword = "1234"
    
    // 渐变背景
    val gradientBrush = Brush.verticalGradient(
        colors = listOf(
            PinkGradientStart.copy(alpha = 0.1f),
            PinkGradientEnd.copy(alpha = 0.05f),
            Color.Transparent
        )
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(gradientBrush)
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {
        // 背景爱心动画
        FloatingHeartsBackground()
        
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(32.dp)
        ) {
            // 锁图标
            LockIcon(isUnlocking = isUnlocking)
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 标题
            Text(
                text = "应用已锁定",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onBackground,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 描述
            Text(
                text = "请输入密码解锁\n保护你们的私密空间",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                lineHeight = 24.sp
            )
            
            Spacer(modifier = Modifier.height(48.dp))
            
            // 密码显示区域
            PasswordDisplay(
                password = enteredPassword,
                isError = isError,
                modifier = Modifier.padding(horizontal = 32.dp)
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 数字键盘
            NumberKeypad(
                onNumberClick = { number ->
                    if (enteredPassword.length < 6) {
                        enteredPassword += number
                        isError = false
                        
                        // 检查密码
                        if (enteredPassword == correctPassword) {
                            isUnlocking = true
                        } else if (enteredPassword.length >= correctPassword.length) {
                            isError = true
                        }
                    }
                },
                onDeleteClick = {
                    if (enteredPassword.isNotEmpty()) {
                        enteredPassword = enteredPassword.dropLast(1)
                        isError = false
                    }
                },
                onBiometricClick = onBiometricAuth
            )
        }
    }
}

/**
 * 锁图标组件
 */
@Composable
private fun LockIcon(
    isUnlocking: Boolean,
    modifier: Modifier = Modifier
) {
    val rotation = remember { Animatable(0f) }
    val scale = remember { Animatable(1f) }
    
    LaunchedEffect(isUnlocking) {
        if (isUnlocking) {
            // 解锁动画
            rotation.animateTo(
                targetValue = 360f,
                animationSpec = tween(800)
            )
            scale.animateTo(
                targetValue = 1.2f,
                animationSpec = tween(400)
            )
        }
    }
    
    Box(
        modifier = modifier
            .size(100.dp)
            .scale(scale.value),
        contentAlignment = Alignment.Center
    ) {
        // 背景光晕
        Canvas(modifier = Modifier.size(120.dp)) {
            drawCircle(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Pink60.copy(alpha = 0.3f),
                        Color.Transparent
                    )
                ),
                radius = size.minDimension / 2
            )
        }
        
        // 锁图标
        Surface(
            modifier = Modifier.size(80.dp),
            shape = CircleShape,
            color = if (isUnlocking) Color.Green.copy(alpha = 0.2f) else Pink60.copy(alpha = 0.2f)
        ) {
            Box(contentAlignment = Alignment.Center) {
                Icon(
                    imageVector = if (isUnlocking) {
                        ImageVector.vectorResource(R.drawable.ic_heart_filled)
                    } else {
                        ImageVector.vectorResource(R.drawable.ic_heart_filled) // 使用爱心代替锁
                    },
                    contentDescription = if (isUnlocking) "解锁中" else "已锁定",
                    tint = if (isUnlocking) Color.Green else Pink60,
                    modifier = Modifier.size(40.dp)
                )
            }
        }
    }
}

/**
 * 密码显示组件
 */
@Composable
private fun PasswordDisplay(
    password: String,
    isError: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        modifier = modifier
    ) {
        repeat(6) { index ->
            val isFilled = index < password.length
            val dotColor = when {
                isError -> Color.Red
                isFilled -> Pink60
                else -> MaterialTheme.colorScheme.outline
            }
            
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .clip(CircleShape)
                    .background(dotColor)
                    .border(
                        width = 2.dp,
                        color = dotColor.copy(alpha = 0.3f),
                        shape = CircleShape
                    )
            )
        }
    }
}

/**
 * 数字键盘组件
 */
@Composable
private fun NumberKeypad(
    onNumberClick: (String) -> Unit,
    onDeleteClick: () -> Unit,
    onBiometricClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = modifier
    ) {
        // 第一行：1, 2, 3
        Row(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
            repeat(3) { index ->
                NumberKey(
                    number = (index + 1).toString(),
                    onClick = { onNumberClick(it) }
                )
            }
        }
        
        // 第二行：4, 5, 6
        Row(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
            repeat(3) { index ->
                NumberKey(
                    number = (index + 4).toString(),
                    onClick = { onNumberClick(it) }
                )
            }
        }
        
        // 第三行：7, 8, 9
        Row(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
            repeat(3) { index ->
                NumberKey(
                    number = (index + 7).toString(),
                    onClick = { onNumberClick(it) }
                )
            }
        }
        
        // 第四行：生物识别, 0, 删除
        Row(horizontalArrangement = Arrangement.spacedBy(24.dp)) {
            // 生物识别按钮
            Surface(
                modifier = Modifier
                    .size(64.dp)
                    .clickable { onBiometricClick() },
                shape = CircleShape,
                color = MaterialTheme.colorScheme.surface,
                shadowElevation = 4.dp
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                        contentDescription = "生物识别",
                        tint = Pink60,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
            
            // 数字0
            NumberKey(
                number = "0",
                onClick = { onNumberClick(it) }
            )
            
            // 删除按钮
            Surface(
                modifier = Modifier
                    .size(64.dp)
                    .clickable { onDeleteClick() },
                shape = CircleShape,
                color = MaterialTheme.colorScheme.surface,
                shadowElevation = 4.dp
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}

/**
 * 数字按键组件
 */
@Composable
private fun NumberKey(
    number: String,
    onClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val scale = remember { Animatable(1f) }
    
    Surface(
        modifier = modifier
            .size(64.dp)
            .scale(scale.value)
            .clickable {
                onClick(number)
            },
        shape = CircleShape,
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        Box(contentAlignment = Alignment.Center) {
            Text(
                text = number,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * 浮动爱心背景
 */
@Composable
private fun FloatingHeartsBackground() {
    val animationProgress = remember { Animatable(0f) }
    
    LaunchedEffect(Unit) {
        animationProgress.animateTo(
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(15000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            )
        )
    }
    
    Canvas(modifier = Modifier.fillMaxSize()) {
        val heartCount = 8
        repeat(heartCount) { index ->
            val progress = (animationProgress.value + index * 0.125f) % 1f
            val x = (index % 4) * (size.width / 3) + (size.width * 0.1f)
            val y = size.height * (1f - progress)
            val alpha = (1f - progress) * 0.1f
            
            if (y > -50f) {
                drawCircle(
                    color = Pink60.copy(alpha = alpha),
                    radius = 8f,
                    center = Offset(x, y)
                )
            }
        }
    }
}