package com.xue.love.ui.components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.theme.CoupleAppTheme
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

/**
 * 配对成功庆祝动画
 * 包含爱心爆炸、烟花效果和庆祝文字
 */
@Composable
fun PairingSuccessAnimation(
    modifier: Modifier = Modifier,
    onAnimationComplete: () -> Unit = {}
) {
    val heartScale = remember { Animatable(0f) }
    val fireworksAlpha = remember { Animatable(0f) }
    val textScale = remember { Animatable(0f) }
    
    LaunchedEffect(Unit) {
        // 爱心放大动画
        heartScale.animateTo(
            targetValue = 1.2f,
            animationSpec = tween(durationMillis = 800)
        )
        
        // 烟花效果
        fireworksAlpha.animateTo(
            targetValue = 1f,
            animationSpec = tween(durationMillis = 500)
        )
        
        delay(300)
        
        // 文字出现
        textScale.animateTo(
            targetValue = 1f,
            animationSpec = tween(durationMillis = 600)
        )
        
        delay(2000)
        onAnimationComplete()
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Color(0xFFFF6B9D).copy(alpha = 0.3f),
                        Color(0xFFE91E63).copy(alpha = 0.2f),
                        Color(0xFF9C27B0).copy(alpha = 0.1f)
                    ),
                    radius = 800f
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        // 烟花背景效果
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            drawFireworks(
                alpha = fireworksAlpha.value,
                canvasSize = size
            )
        }
        
        // 主要内容
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(32.dp)
        ) {
            // 爱心图标
            Box(
                modifier = Modifier
                    .size(150.dp)
                    .scale(heartScale.value)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.3f),
                                Color.White.copy(alpha = 0.1f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = "配对成功",
                    modifier = Modifier.size(80.dp),
                    tint = Color(0xFFE91E63)
                )
            }
            
            // 庆祝文字
            Column(
                modifier = Modifier.scale(textScale.value),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "🎉 配对成功！🎉",
                    style = MaterialTheme.typography.displayMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "恭喜你们建立了专属连接\n现在可以开始甜蜜的私密之旅了",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.White.copy(alpha = 0.9f),
                    textAlign = TextAlign.Center,
                    lineHeight = MaterialTheme.typography.bodyLarge.lineHeight * 1.3
                )
            }
        }
        
        // 爱心粒子效果
        HeartParticleEffect(
            particleCount = 30,
            colors = listOf(
                Color(0xFFFF6B9D),
                Color(0xFFE91E63),
                Color(0xFFFFE0E6),
                Color(0xFFC44569)
            )
        )
    }
}

/**
 * 绘制烟花效果
 */
private fun DrawScope.drawFireworks(
    alpha: Float,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val fireworksCount = 8
    val colors = listOf(
        Color(0xFFFF6B9D),
        Color(0xFFE91E63),
        Color(0xFF9C27B0),
        Color(0xFFFF5722),
        Color(0xFFFFE0E6)
    )
    
    repeat(fireworksCount) { index ->
        val centerX = Random.nextFloat() * canvasSize.width
        val centerY = Random.nextFloat() * canvasSize.height * 0.6f + canvasSize.height * 0.2f
        val color = colors[index % colors.size].copy(alpha = alpha)
        
        // 绘制烟花爆炸效果
        repeat(12) { sparkIndex ->
            val angle = sparkIndex * 30f * Math.PI / 180f
            val length = 50f + Random.nextFloat() * 30f
            
            val endX = centerX + cos(angle).toFloat() * length
            val endY = centerY + sin(angle).toFloat() * length
            
            drawLine(
                color = color,
                start = Offset(centerX, centerY),
                end = Offset(endX, endY),
                strokeWidth = 3f
            )
            
            // 绘制火花点
            drawCircle(
                color = color,
                radius = 4f,
                center = Offset(endX, endY)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PairingSuccessAnimationPreview() {
    CoupleAppTheme {
        PairingSuccessAnimation()
    }
}