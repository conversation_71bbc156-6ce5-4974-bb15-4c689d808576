package com.xue.love.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.xue.love.ui.theme.*

/**
 * 主题选择器组件
 * 提供美观的主题切换界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ThemeSelector(
    modifier: Modifier = Modifier,
    themeManager: ThemeManager = rememberThemeManager()
) {
    val currentTheme by themeManager.currentTheme.collectAsState()
    val isDarkMode by themeManager.isDarkMode.collectAsState()
    val isCoupleSync by themeManager.isCoupleSync.collectAsState()
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 标题
            Text(
                text = "主题设置",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            // 主题选择
            Text(
                text = "选择主题风格",
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(horizontal = 4.dp)
            ) {
                items(CoupleThemeType.values()) { themeType ->
                    ThemePreviewCard(
                        themeType = themeType,
                        isSelected = currentTheme == themeType,
                        onClick = { themeManager.switchTheme(themeType) },
                        themeManager = themeManager
                    )
                }
            }
            
            Divider(
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                thickness = 1.dp
            )
            
            // 深色模式切换
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = if (isDarkMode) Icons.Default.Star else Icons.Default.Star,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = if (isDarkMode) "深色模式" else "浅色模式",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Switch(
                    checked = isDarkMode,
                    onCheckedChange = { themeManager.toggleDarkMode() },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = MaterialTheme.colorScheme.primary,
                        checkedTrackColor = MaterialTheme.colorScheme.primaryContainer
                    )
                )
            }
            
            // 情侣同步切换
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Column {
                        Text(
                            text = "情侣主题同步",
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "与伴侣同步主题设置",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Switch(
                    checked = isCoupleSync,
                    onCheckedChange = { themeManager.toggleCoupleSync() },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = MaterialTheme.colorScheme.primary,
                        checkedTrackColor = MaterialTheme.colorScheme.primaryContainer
                    )
                )
            }
        }
    }
}

/**
 * 主题预览卡片
 */
@Composable
private fun ThemePreviewCard(
    themeType: CoupleThemeType,
    isSelected: Boolean,
    onClick: () -> Unit,
    themeManager: ThemeManager,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = tween(300),
        label = "scale"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) MaterialTheme.colorScheme.primary else Color.Transparent,
        animationSpec = tween(300),
        label = "borderColor"
    )
    
    // 获取主题对应的渐变色
    val gradient = when (themeType) {
        CoupleThemeType.SWEET_PINK -> Brush.linearGradient(
            colors = listOf(SweetPinkGradientStart, SweetPinkGradientEnd)
        )
        CoupleThemeType.PASSION_RED -> Brush.linearGradient(
            colors = listOf(PassionRedGradientStart, PassionRedGradientEnd)
        )
        CoupleThemeType.MYSTERY_PURPLE -> Brush.linearGradient(
            colors = listOf(MysteryPurpleGradientStart, MysteryPurpleGradientEnd)
        )
        CoupleThemeType.ELEGANT_BLACK -> Brush.linearGradient(
            colors = listOf(ElegantBlackGradientStart, ElegantBlackGradientEnd)
        )
    }
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .scale(scale)
                .clip(CircleShape)
                .border(
                    width = if (isSelected) 3.dp else 0.dp,
                    color = borderColor,
                    shape = CircleShape
                )
                .background(gradient)
                .clickable { onClick() },
            contentAlignment = Alignment.Center
        ) {
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Text(
            text = themeManager.getThemeDisplayName(themeType),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 简化版主题选择器（用于设置页面等）
 */
@Composable
fun CompactThemeSelector(
    modifier: Modifier = Modifier,
    themeManager: ThemeManager = rememberThemeManager()
) {
    val currentTheme by themeManager.currentTheme.collectAsState()
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(CoupleThemeType.values()) { themeType ->
            val isSelected = currentTheme == themeType
            val scale by animateFloatAsState(
                targetValue = if (isSelected) 1.2f else 1f,
                animationSpec = tween(200),
                label = "compactScale"
            )
            
            val gradient = when (themeType) {
                CoupleThemeType.SWEET_PINK -> Brush.linearGradient(
                    colors = listOf(SweetPinkGradientStart, SweetPinkGradientEnd)
                )
                CoupleThemeType.PASSION_RED -> Brush.linearGradient(
                    colors = listOf(PassionRedGradientStart, PassionRedGradientEnd)
                )
                CoupleThemeType.MYSTERY_PURPLE -> Brush.linearGradient(
                    colors = listOf(MysteryPurpleGradientStart, MysteryPurpleGradientEnd)
                )
                CoupleThemeType.ELEGANT_BLACK -> Brush.linearGradient(
                    colors = listOf(ElegantBlackGradientStart, ElegantBlackGradientEnd)
                )
            }
            
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .scale(scale)
                    .clip(CircleShape)
                    .background(gradient)
                    .border(
                        width = if (isSelected) 2.dp else 0.dp,
                        color = MaterialTheme.colorScheme.primary,
                        shape = CircleShape
                    )
                    .clickable { themeManager.switchTheme(themeType) },
                contentAlignment = Alignment.Center
            ) {
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}