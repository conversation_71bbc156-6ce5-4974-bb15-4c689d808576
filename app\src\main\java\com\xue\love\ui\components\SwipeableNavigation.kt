package com.xue.love.ui.components

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import kotlin.math.abs

/**
 * 支持手势导航的容器组件
 * 提供左右滑动切换页面功能
 */
@Composable
fun SwipeableNavigation(
    selectedTab: Int,
    onTabChanged: (Int) -> Unit,
    modifier: Modifier = Modifier,
    swipeThreshold: Float = 100f,
    content: @Composable (Int, Float) -> Unit
) {
    val density = LocalDensity.current
    val scope = rememberCoroutineScope()
    
    var dragOffset by remember { mutableStateOf(0f) }
    var isDragging by remember { mutableStateOf(false) }
    
    val offsetX = remember { Animatable(0f) }
    
    LaunchedEffect(selectedTab) {
        if (!isDragging) {
            offsetX.animateTo(0f, animationSpec = tween(300))
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(selectedTab) {
                detectDragGestures(
                    onDragStart = { 
                        isDragging = true
                        dragOffset = 0f
                    },
                    onDragEnd = {
                        isDragging = false
                        val threshold = with(density) { swipeThreshold.dp.toPx() }
                        
                        when {
                            dragOffset > threshold && selectedTab > 0 -> {
                                // 向右滑动，切换到上一个页面
                                onTabChanged(selectedTab - 1)
                            }
                            dragOffset < -threshold && selectedTab < 2 -> {
                                // 向左滑动，切换到下一个页面
                                onTabChanged(selectedTab + 1)
                            }
                            else -> {
                                // 回弹到原位置
                                scope.launch {
                                    offsetX.animateTo(0f, animationSpec = tween(300))
                                }
                            }
                        }
                        dragOffset = 0f
                    }
                ) { change, _ ->
                    dragOffset += change.x
                    scope.launch {
                        // 限制拖拽范围
                        val maxOffset = with(density) { 150.dp.toPx() }
                        val clampedOffset = dragOffset.coerceIn(-maxOffset, maxOffset)
                        offsetX.snapTo(clampedOffset)
                    }
                }
            }
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    translationX = offsetX.value
                    // 添加轻微的缩放效果
                    val scale = 1f - (abs(offsetX.value) / 1000f).coerceAtMost(0.05f)
                    scaleX = scale
                    scaleY = scale
                }
        ) {
            content(selectedTab, offsetX.value)
        }
        
        // 滑动指示器
        if (isDragging && abs(dragOffset) > 20f) {
            SwipeIndicator(
                direction = if (dragOffset > 0) SwipeDirection.RIGHT else SwipeDirection.LEFT,
                progress = (abs(dragOffset) / with(density) { swipeThreshold.dp.toPx() }).coerceAtMost(1f),
                canSwipe = when {
                    dragOffset > 0 && selectedTab > 0 -> true
                    dragOffset < 0 && selectedTab < 2 -> true
                    else -> false
                }
            )
        }
    }
}

/**
 * 滑动方向枚举
 */
enum class SwipeDirection {
    LEFT, RIGHT
}

/**
 * 滑动指示器组件
 */
@Composable
private fun SwipeIndicator(
    direction: SwipeDirection,
    progress: Float,
    canSwipe: Boolean,
    modifier: Modifier = Modifier
) {
    val targetPage = when (direction) {
        SwipeDirection.LEFT -> "下一页"
        SwipeDirection.RIGHT -> "上一页"
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Card(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = if (canSwipe) {
                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f + progress * 0.2f)
                } else {
                    MaterialTheme.colorScheme.error.copy(alpha = 0.1f + progress * 0.2f)
                }
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = if (canSwipe) "继续滑动切换到$targetPage" else "无法切换",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (canSwipe) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.error
                    }
                )
            }
        }
    }
}

/**
 * 页面切换动画效果
 */
@Composable
fun PageTransitionEffect(
    offset: Float,
    content: @Composable () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .graphicsLayer {
                // 根据偏移量调整透明度和缩放
                val normalizedOffset = (abs(offset) / 500f).coerceAtMost(1f)
                alpha = 1f - normalizedOffset * 0.3f
                scaleX = 1f - normalizedOffset * 0.05f
                scaleY = 1f - normalizedOffset * 0.05f
            }
    ) {
        content()
    }
}
