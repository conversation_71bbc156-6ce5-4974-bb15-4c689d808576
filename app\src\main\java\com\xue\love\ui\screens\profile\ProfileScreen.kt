package com.xue.love.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.components.CoupleStatsCard
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.components.RomanticCard
import com.xue.love.ui.components.ThemeSelector
import com.xue.love.ui.theme.CoupleAppTheme
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 成就数据类
 */
data class Achievement(
    val id: String,
    val title: String,
    val description: String,
    val icon: Int,
    val isUnlocked: Boolean,
    val color: Color
)

/**
 * 设置项数据类
 */
data class SettingItem(
    val title: String,
    val subtitle: String,
    val icon: Int,
    val hasSwitch: Boolean = false,
    val switchState: Boolean = false,
    val onClick: () -> Unit = {}
)

/**
 * 个性化个人中心界面
 * 包含用户信息、统计数据、成就系统、设置等
 */
@Composable
fun ProfileScreen(
    modifier: Modifier = Modifier,
    onThemeChanged: (CoupleThemeType) -> Unit = {},
    onSettingClick: (String) -> Unit = {}
) {
    var selectedTheme by remember { mutableStateOf(CoupleThemeType.SWEET_PINK) }
    var isDarkMode by remember { mutableStateOf(false) }
    var isPrivacyMode by remember { mutableStateOf(false) }
    var isNotificationEnabled by remember { mutableStateOf(true) }
    
    // 示例成就数据
    val achievements = remember {
        listOf(
            Achievement(
                "1", "初次相遇", "完成第一次配对", 
                R.drawable.ic_heart_filled, true, Color(0xFFE91E63)
            ),
            Achievement(
                "2", "甜蜜聊天", "发送100条消息", 
                R.drawable.ic_chat_filled, true, Color(0xFF9C27B0)
            ),
            Achievement(
                "3", "爱心达人", "发送50个爱心", 
                R.drawable.ic_heart_filled, true, Color(0xFFFF6B9D)
            ),
            Achievement(
                "4", "亲密无间", "亲密度达到90", 
                R.drawable.ic_couple_love, false, Color(0xFFFF5722)
            ),
            Achievement(
                "5", "长久陪伴", "在一起100天", 
                R.drawable.ic_heart_filled, false, Color(0xFF673AB7)
            )
        )
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        RomanticBackground(
            themeType = CoupleThemeType.SWEET_PINK
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                item {
                    // 个人信息卡片
                    RomanticCard {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // 头像
                            Box(
                                modifier = Modifier
                                    .size(100.dp)
                                    .clip(CircleShape)
                                    .background(
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color(0xFFFF6B9D),
                                                Color(0xFFC44569)
                                            )
                                        )
                                    )
                                    .border(
                                        width = 3.dp,
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color.White.copy(alpha = 0.8f),
                                                Color.White.copy(alpha = 0.4f)
                                            )
                                        ),
                                        shape = CircleShape
                                    )
                                    .clickable { /* 更换头像 */ },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
                                    contentDescription = "头像",
                                    modifier = Modifier.size(50.dp),
                                    tint = Color.White
                                )
                            }
                            
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Text(
                                    text = "我的爱人",
                                    style = MaterialTheme.typography.headlineMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFF2D1B20)
                                )
                                
                                Text(
                                    text = "与 TA 在一起 365 天",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                                )
                                
                                Text(
                                    text = "💕 爱情等级：甜蜜恋人 💕",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFFE91E63),
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
                
                item {
                    // 统计数据
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "我们的数据",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(horizontal = 4.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            CoupleStatsCard(
                                title = "消息数",
                                value = "2,468",
                                subtitle = "条甜蜜对话",
                                icon = ImageVector.vectorResource(R.drawable.ic_chat_filled),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFF9C27B0), Color(0xFF673AB7))
                            )
                            
                            CoupleStatsCard(
                                title = "爱心数",
                                value = "1,234",
                                subtitle = "个爱的表达",
                                icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFE91E63), Color(0xFFFF5722))
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            CoupleStatsCard(
                                title = "互动次数",
                                value = "5,678",
                                subtitle = "次亲密互动",
                                icon = ImageVector.vectorResource(R.drawable.ic_vibration),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFFF6B9D), Color(0xFFFF8FA3))
                            )
                            
                            CoupleStatsCard(
                                title = "共同回忆",
                                value = "89",
                                subtitle = "张珍贵照片",
                                icon = ImageVector.vectorResource(R.drawable.ic_intimate_message),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFFF5722), Color(0xFFFF8A65))
                            )
                        }
                    }
                }
                
                item {
                    // 成就系统
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "成就徽章",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20)
                            )
                            
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                items(achievements) { achievement ->
                                    AchievementBadge(
                                        achievement = achievement,
                                        onClick = { /* 查看成就详情 */ }
                                    )
                                }
                            }
                        }
                    }
                }
                
                item {
                    // 主题选择
                    ThemeSelector()
                }
                
                item {
                    // 设置选项
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "设置",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20)
                            )
                            
                            SettingItemRow(
                                title = "夜间模式",
                                subtitle = "保护眼睛，营造浪漫氛围",
                                icon = R.drawable.ic_heart_filled,
                                hasSwitch = true,
                                switchState = isDarkMode,
                                onSwitchChanged = { isDarkMode = it }
                            )
                            
                            SettingItemRow(
                                title = "隐私模式",
                                subtitle = "隐藏通知内容预览",
                                icon = R.drawable.ic_intimate_message,
                                hasSwitch = true,
                                switchState = isPrivacyMode,
                                onSwitchChanged = { isPrivacyMode = it }
                            )
                            
                            SettingItemRow(
                                title = "推送通知",
                                subtitle = "接收爱人的消息提醒",
                                icon = R.drawable.ic_vibration,
                                hasSwitch = true,
                                switchState = isNotificationEnabled,
                                onSwitchChanged = { isNotificationEnabled = it }
                            )
                            
                            SettingItemRow(
                                title = "私密相册",
                                subtitle = "管理我们的甜蜜回忆",
                                icon = R.drawable.ic_couple_love,
                                onClick = { onSettingClick("private_album") }
                            )
                            
                            SettingItemRow(
                                title = "数据备份",
                                subtitle = "备份聊天记录和照片",
                                icon = R.drawable.ic_chat_filled,
                                onClick = { onSettingClick("backup") }
                            )
                            
                            SettingItemRow(
                                title = "关于我们",
                                subtitle = "应用信息和帮助",
                                icon = R.drawable.ic_heart_filled,
                                onClick = { onSettingClick("about") }
                            )
                        }
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                }
            }
        }
    }
}

/**
 * 成就徽章组件
 */
@Composable
private fun AchievementBadge(
    achievement: Achievement,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable { onClick() }
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(
                    if (achievement.isUnlocked) {
                        Brush.radialGradient(
                            colors = listOf(
                                achievement.color.copy(alpha = 0.3f),
                                achievement.color.copy(alpha = 0.1f)
                            )
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                Color.Gray.copy(alpha = 0.2f),
                                Color.Gray.copy(alpha = 0.1f)
                            )
                        )
                    }
                )
                .border(
                    width = 2.dp,
                    color = if (achievement.isUnlocked) achievement.color else Color.Gray.copy(alpha = 0.5f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(achievement.icon),
                contentDescription = achievement.title,
                modifier = Modifier.size(30.dp),
                tint = if (achievement.isUnlocked) achievement.color else Color.Gray.copy(alpha = 0.5f)
            )
        }
        
        Text(
            text = achievement.title,
            style = MaterialTheme.typography.bodySmall,
            color = if (achievement.isUnlocked) Color(0xFF2D1B20) else Color(0xFF2D1B20).copy(alpha = 0.5f),
            textAlign = TextAlign.Center,
            fontWeight = if (achievement.isUnlocked) FontWeight.Medium else FontWeight.Normal
        )
    }
}

/**
 * 设置项行组件
 */
@Composable
private fun SettingItemRow(
    title: String,
    subtitle: String,
    icon: Int,
    hasSwitch: Boolean = false,
    switchState: Boolean = false,
    onSwitchChanged: (Boolean) -> Unit = {},
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { if (!hasSwitch) onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(icon),
            contentDescription = title,
            modifier = Modifier.size(24.dp),
            tint = Color(0xFFFF6B9D)
        )
        
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
        
        if (hasSwitch) {
            Switch(
                checked = switchState,
                onCheckedChange = onSwitchChanged,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = Color(0xFFFF6B9D),
                    uncheckedThumbColor = Color.White,
                    uncheckedTrackColor = Color.Gray.copy(alpha = 0.3f)
                )
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProfileScreenPreview() {
    CoupleAppTheme {
        ProfileScreen()
    }
}