package com.xue.love.ui.screens.chat

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.BorderStroke
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.components.HeartParticleEffect
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.theme.CoupleAppTheme
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 消息数据类
 */
data class ChatMessage(
    val id: String,
    val content: String,
    val isFromMe: Boolean,
    val timestamp: Long,
    val type: MessageType = MessageType.TEXT,
    val hasSpecialEffect: Boolean = false
)

enum class MessageType {
    TEXT, HEART, VIBRATION, KISS, IMAGE
}

/**
 * 沉浸式聊天界面
 * 包含美观的消息气泡、情趣表情包和特效动画
 */
@Composable
fun ChatScreen(
    modifier: Modifier = Modifier,
    onSendMessage: (String) -> Unit = {},
    onSendHeart: () -> Unit = {},
    onSendVibration: () -> Unit = {},
    onSendKiss: () -> Unit = {}
) {
    var messageText by remember { mutableStateOf("") }
    var showSpecialEffects by remember { mutableStateOf(false) }
    
    // 示例消息数据
    val messages = remember {
        listOf(
            ChatMessage("1", "你好呀，我的爱人 💕", false, System.currentTimeMillis() - 300000),
            ChatMessage("2", "想你了~", true, System.currentTimeMillis() - 240000),
            ChatMessage("3", "我也想你呢，宝贝 😘", false, System.currentTimeMillis() - 180000, hasSpecialEffect = true),
            ChatMessage("4", "今天过得怎么样？", true, System.currentTimeMillis() - 120000),
            ChatMessage("5", "有你的每一天都很美好 ❤️", false, System.currentTimeMillis() - 60000),
            ChatMessage("6", "", true, System.currentTimeMillis() - 30000, MessageType.HEART),
            ChatMessage("7", "收到你的爱心啦！好开心 🥰", false, System.currentTimeMillis())
        )
    }
    
    val listState = rememberLazyListState()
    
    Box(modifier = modifier.fillMaxSize()) {
        // 浪漫背景
        RomanticBackground(
            themeType = CoupleThemeType.MYSTERY_PURPLE
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 聊天标题栏
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color.White.copy(alpha = 0.1f),
                    shadowElevation = 4.dp
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "私密聊天",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Icon(
                            imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
                
                // 消息列表
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    state = listState,
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(messages) { message ->
                        ChatMessageBubble(
                            message = message,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
                
                // 输入区域
                ChatInputArea(
                    messageText = messageText,
                    onMessageTextChange = { messageText = it },
                    onSendMessage = {
                        if (messageText.isNotBlank()) {
                            onSendMessage(messageText)
                            messageText = ""
                        }
                    },
                    onSendHeart = {
                        onSendHeart()
                        showSpecialEffects = true
                    },
                    onSendVibration = onSendVibration,
                    onSendKiss = onSendKiss
                )
            }
        }
        
        // 特效动画
        AnimatedVisibility(
            visible = showSpecialEffects,
            enter = fadeIn(),
            exit = fadeOut()
        ) {
            HeartParticleEffect(
                particleCount = 20,
                colors = listOf(
                    Color(0xFFE91E63),
                    Color(0xFFFF6B9D),
                    Color(0xFFFFE0E6)
                )
            )
        }
    }
}

/**
 * 聊天消息气泡
 */
@Composable
private fun ChatMessageBubble(
    message: ChatMessage,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "messageEffect")
    val specialEffectScale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (message.hasSpecialEffect) 1.05f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "specialEffect"
    )
    
    Row(
        modifier = modifier,
        horizontalArrangement = if (message.isFromMe) Arrangement.End else Arrangement.Start
    ) {
        if (!message.isFromMe) {
            // 对方头像
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFFF6B9D),
                                Color(0xFFC44569)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
        }
        
        // 消息气泡
        Surface(
            modifier = Modifier
                .scale(specialEffectScale)
                .fillMaxWidth(0.8f),
            shape = RoundedCornerShape(
                topStart = if (message.isFromMe) 20.dp else 4.dp,
                topEnd = if (message.isFromMe) 4.dp else 20.dp,
                bottomStart = 20.dp,
                bottomEnd = 20.dp
            ),
            color = if (message.isFromMe) {
                Color(0xFFFF6B9D).copy(alpha = 0.9f)
            } else {
                Color.White.copy(alpha = 0.95f)
            },
            shadowElevation = 4.dp
        ) {
            Box(
                modifier = Modifier.padding(16.dp)
            ) {
                when (message.type) {
                    MessageType.TEXT -> {
                        Text(
                            text = message.content,
                            style = MaterialTheme.typography.bodyMedium,
                            color = if (message.isFromMe) Color.White else Color(0xFF2D1B20),
                            lineHeight = MaterialTheme.typography.bodyMedium.lineHeight * 1.2
                        )
                    }
                    MessageType.HEART -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                                contentDescription = "爱心",
                                tint = Color(0xFFE91E63),
                                modifier = Modifier.size(24.dp)
                            )
                            Text(
                                text = "发送了一个爱心",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (message.isFromMe) Color.White else Color(0xFF2D1B20),
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                    }
                    MessageType.VIBRATION -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_vibration),
                                contentDescription = "震动",
                                tint = Color(0xFFFF5722),
                                modifier = Modifier.size(24.dp)
                            )
                            Text(
                                text = "发送了震动消息",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (message.isFromMe) Color.White else Color(0xFF2D1B20),
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                    }
                    MessageType.KISS -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = ImageVector.vectorResource(R.drawable.ic_kiss),
                                contentDescription = "飞吻",
                                tint = Color(0xFF9C27B0),
                                modifier = Modifier.size(24.dp)
                            )
                            Text(
                                text = "给你一个飞吻",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (message.isFromMe) Color.White else Color(0xFF2D1B20),
                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                            )
                        }
                    }
                    MessageType.IMAGE -> {
                        // 图片消息处理
                    }
                }
            }
        }
        
        if (message.isFromMe) {
            Spacer(modifier = Modifier.width(8.dp))
            
            // 我的头像
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                Color(0xFF9C27B0),
                                Color(0xFF673AB7)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

/**
 * 聊天输入区域
 */
@Composable
private fun ChatInputArea(
    messageText: String,
    onMessageTextChange: (String) -> Unit,
    onSendMessage: () -> Unit,
    onSendHeart: () -> Unit,
    onSendVibration: () -> Unit,
    onSendKiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showEmojiPanel by remember { mutableStateOf(false) }
    
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White,
        shadowElevation = 8.dp
    ) {
        Column {
            // 主输入区域
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 表情按钮
                IconButton(
                    onClick = { showEmojiPanel = !showEmojiPanel },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFF5F5F5))
                ) {
                    Text(
                        text = "😊",
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                
                // 文字输入框
                Surface(
                    modifier = Modifier.weight(1f),
                    shape = RoundedCornerShape(20.dp),
                    color = Color(0xFFF8F8F8),
                    border = androidx.compose.foundation.BorderStroke(
                        1.dp, 
                        Color(0xFFFF6B9D).copy(alpha = 0.3f)
                    )
                ) {
                    BasicTextField(
                        value = messageText,
                        onValueChange = onMessageTextChange,
                        modifier = Modifier
                            .padding(horizontal = 16.dp, vertical = 12.dp)
                            .fillMaxWidth(),
                        textStyle = MaterialTheme.typography.bodyMedium.copy(
                            color = Color(0xFF2D1B20)
                        ),
                        cursorBrush = SolidColor(Color(0xFFFF6B9D)),
                        decorationBox = { innerTextField ->
                            if (messageText.isEmpty()) {
                                Text(
                                    text = "输入甜蜜的话语...",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2D1B20).copy(alpha = 0.5f)
                                )
                            }
                            innerTextField()
                        }
                    )
                }
                
                // 图片按钮
                IconButton(
                    onClick = { /* TODO: 选择图片 */ },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFF5F5F5))
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_intimate_message),
                        contentDescription = "选择图片",
                        tint = Color(0xFF9C27B0),
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                // 拍摄按钮
                IconButton(
                    onClick = { /* TODO: 拍摄照片 */ },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFF5F5F5))
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_vibration),
                        contentDescription = "拍摄",
                        tint = Color(0xFFFF5722),
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                // 发送按钮
                IconButton(
                    onClick = onSendMessage,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFFFF6B9D),
                                    Color(0xFFC44569)
                                )
                            )
                        )
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_chat_filled),
                        contentDescription = "发送",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            // 表情和快捷操作面板
            AnimatedVisibility(
                visible = showEmojiPanel,
                enter = slideInVertically(initialOffsetY = { it }) + fadeIn(),
                exit = slideOutVertically(targetOffsetY = { it }) + fadeOut()
            ) {
                Surface(
                    modifier = Modifier.fillMaxWidth(),
                    color = Color(0xFFFAFAFA)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // 情侣专属表情
                        Text(
                            text = "情侣专属",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2D1B20),
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            EmojiButton(
                                onClick = onSendHeart,
                                icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                                label = "爱心",
                                color = Color(0xFFE91E63)
                            )
                            
                            EmojiButton(
                                onClick = onSendVibration,
                                icon = ImageVector.vectorResource(R.drawable.ic_vibration),
                                label = "震动",
                                color = Color(0xFFFF5722)
                            )
                            
                            EmojiButton(
                                onClick = onSendKiss,
                                icon = ImageVector.vectorResource(R.drawable.ic_kiss),
                                label = "飞吻",
                                color = Color(0xFF9C27B0)
                            )
                            
                            EmojiButton(
                                onClick = { /* TODO: 拥抱 */ },
                                icon = ImageVector.vectorResource(R.drawable.ic_couple_love),
                                label = "拥抱",
                                color = Color(0xFF673AB7)
                            )
                        }
                        
                        // 常用表情
                        Text(
                            text = "常用表情",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2D1B20),
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            listOf("😘", "🥰", "😍", "💕", "❤️", "💖").forEach { emoji ->
                                Surface(
                                    modifier = Modifier
                                        .size(48.dp)
                                        .clickable {
                                            onMessageTextChange(messageText + emoji)
                                        },
                                    shape = CircleShape,
                                    color = Color.White,
                                    shadowElevation = 2.dp
                                ) {
                                    Box(
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = emoji,
                                            style = MaterialTheme.typography.headlineSmall
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * 表情按钮组件
 */
@Composable
private fun EmojiButton(
    onClick: () -> Unit,
    icon: ImageVector,
    label: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp),
        modifier = modifier.clickable { onClick() }
    ) {
        Surface(
            modifier = Modifier.size(56.dp),
            shape = CircleShape,
            color = color.copy(alpha = 0.1f),
            shadowElevation = 4.dp
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = label,
                    tint = color,
                    modifier = Modifier.size(28.dp)
                )
            }
        }
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ChatScreenPreview() {
    CoupleAppTheme {
        ChatScreen()
    }
}