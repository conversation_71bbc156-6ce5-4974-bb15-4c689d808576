package com.xue.love.ui.screens.splash

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.R
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart
import kotlinx.coroutines.delay
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

/**
 * 精美的情侣APP启动画面
 * 包含情侣插画、品牌动画和浪漫特效
 */
@Composable
fun SplashScreen(
    onSplashFinished: () -> Unit,
    modifier: Modifier = Modifier
) {
    var animationPhase by remember { mutableStateOf(0) }
    
    // 背景渐变
    val gradientBrush = Brush.radialGradient(
        colors = listOf(
            PinkGradientStart.copy(alpha = 0.3f),
            PinkGradientEnd.copy(alpha = 0.1f),
            Color.Transparent
        ),
        radius = 800f
    )
    
    LaunchedEffect(Unit) {
        // 动画序列
        delay(500)
        animationPhase = 1 // Logo出现
        delay(1000)
        animationPhase = 2 // 文字出现
        delay(1000)
        animationPhase = 3 // 爱心飘落
        delay(1500)
        onSplashFinished()
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(gradientBrush)
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {
        // 背景爱心粒子效果
        FloatingHeartsBackground()
        
        // 主要内容
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(32.dp)
        ) {
            // Logo动画
            AnimatedLogo(
                isVisible = animationPhase >= 1,
                modifier = Modifier.size(120.dp)
            )
            
            // 应用名称
            AnimatedAppName(
                isVisible = animationPhase >= 2,
                modifier = Modifier.padding(top = 24.dp)
            )
            
            // 副标题
            AnimatedSubtitle(
                isVisible = animationPhase >= 2,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
        
        // 底部加载指示器
        if (animationPhase >= 3) {
            LoadingIndicator(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 64.dp)
            )
        }
    }
}

/**
 * 动画Logo组件
 */
@Composable
private fun AnimatedLogo(
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    val scale = remember { Animatable(0f) }
    val rotation = remember { Animatable(0f) }
    
    LaunchedEffect(isVisible) {
        if (isVisible) {
            // 缩放动画
            scale.animateTo(
                targetValue = 1f,
                animationSpec = tween(800)
            )
            // 心跳动画
            scale.animateTo(
                targetValue = 1.1f,
                animationSpec = infiniteRepeatable(
                    animation = tween(1000),
                    repeatMode = RepeatMode.Reverse
                )
            )
        }
    }
    
    LaunchedEffect(isVisible) {
        if (isVisible) {
            delay(500)
            rotation.animateTo(
                targetValue = 360f,
                animationSpec = tween(1000, easing = LinearEasing)
            )
        }
    }
    
    Box(
        modifier = modifier
            .scale(scale.value)
            .alpha(if (isVisible) 1f else 0f),
        contentAlignment = Alignment.Center
    ) {
        // 背景光晕
        Canvas(modifier = Modifier.size(140.dp)) {
            drawCircle(
                brush = Brush.radialGradient(
                    colors = listOf(
                        Pink60.copy(alpha = 0.3f),
                        Color.Transparent
                    )
                ),
                radius = size.minDimension / 2
            )
        }
        
        // 主Logo
        Icon(
            imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
            contentDescription = "情侣APP Logo",
            tint = Pink60,
            modifier = Modifier.size(80.dp)
        )
    }
}

/**
 * 动画应用名称
 */
@Composable
private fun AnimatedAppName(
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    val alpha = remember { Animatable(0f) }
    
    LaunchedEffect(isVisible) {
        if (isVisible) {
            alpha.animateTo(
                targetValue = 1f,
                animationSpec = tween(600)
            )
        }
    }
    
    Text(
        text = "情侣空间",
        style = MaterialTheme.typography.headlineLarge.copy(
            fontSize = 32.sp,
            fontWeight = FontWeight.Bold
        ),
        color = MaterialTheme.colorScheme.onBackground,
        textAlign = TextAlign.Center,
        modifier = modifier.alpha(alpha.value)
    )
}

/**
 * 动画副标题
 */
@Composable
private fun AnimatedSubtitle(
    isVisible: Boolean,
    modifier: Modifier = Modifier
) {
    val alpha = remember { Animatable(0f) }
    
    LaunchedEffect(isVisible) {
        if (isVisible) {
            delay(300)
            alpha.animateTo(
                targetValue = 1f,
                animationSpec = tween(600)
            )
        }
    }
    
    Text(
        text = "专属于你们的私密世界",
        style = MaterialTheme.typography.bodyLarge,
        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
        textAlign = TextAlign.Center,
        modifier = modifier.alpha(alpha.value)
    )
}

/**
 * 加载指示器
 */
@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    val progress = remember { Animatable(0f) }
    
    LaunchedEffect(Unit) {
        progress.animateTo(
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(2000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            )
        )
    }
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        // 脉冲圆点
        Canvas(modifier = Modifier.size(40.dp)) {
            val center = Offset(size.width / 2, size.height / 2)
            val maxRadius = size.minDimension / 4
            
            for (i in 0..2) {
                val phase = (progress.value + i * 0.3f) % 1f
                val radius = maxRadius * phase
                val alpha = 1f - phase
                
                drawCircle(
                    color = Pink60.copy(alpha = alpha),
                    radius = radius,
                    center = center
                )
            }
        }
        
        Text(
            text = "正在为你们准备...",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f),
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}

/**
 * 浮动爱心背景效果
 */
@Composable
private fun FloatingHeartsBackground(
    modifier: Modifier = Modifier
) {
    val hearts = remember {
        List(15) {
            HeartParticle(
                x = Random.nextFloat(),
                y = Random.nextFloat() + 1f, // 从底部开始
                speed = Random.nextFloat() * 0.5f + 0.2f,
                size = Random.nextFloat() * 20f + 10f,
                alpha = Random.nextFloat() * 0.3f + 0.1f
            )
        }
    }
    
    val animationProgress = remember { Animatable(0f) }
    
    LaunchedEffect(Unit) {
        animationProgress.animateTo(
            targetValue = 1f,
            animationSpec = infiniteRepeatable(
                animation = tween(10000, easing = LinearEasing),
                repeatMode = RepeatMode.Restart
            )
        )
    }
    
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        hearts.forEach { heart ->
            val currentY = heart.y - (animationProgress.value * heart.speed * 2f)
            if (currentY > -0.2f) { // 只绘制可见的爱心
                drawHeart(
                    center = Offset(
                        x = heart.x * size.width,
                        y = currentY * size.height
                    ),
                    size = heart.size,
                    color = Pink60.copy(alpha = heart.alpha)
                )
            }
        }
    }
}

/**
 * 爱心粒子数据类
 */
private data class HeartParticle(
    val x: Float,
    val y: Float,
    val speed: Float,
    val size: Float,
    val alpha: Float
)

/**
 * 绘制爱心形状
 */
private fun DrawScope.drawHeart(
    center: Offset,
    size: Float,
    color: Color
) {
    val path = androidx.compose.ui.graphics.Path()
    
    // 简化的爱心形状
    val heartWidth = size
    val heartHeight = size * 0.8f
    
    // 左半圆
    path.addOval(
        androidx.compose.ui.geometry.Rect(
            offset = Offset(
                center.x - heartWidth * 0.5f,
                center.y - heartHeight * 0.3f
            ),
            size = androidx.compose.ui.geometry.Size(
                heartWidth * 0.5f,
                heartHeight * 0.6f
            )
        )
    )
    
    // 右半圆
    path.addOval(
        androidx.compose.ui.geometry.Rect(
            offset = Offset(
                center.x,
                center.y - heartHeight * 0.3f
            ),
            size = androidx.compose.ui.geometry.Size(
                heartWidth * 0.5f,
                heartHeight * 0.6f
            )
        )
    )
    
    // 下方三角形
    path.moveTo(center.x - heartWidth * 0.5f, center.y)
    path.lineTo(center.x, center.y + heartHeight * 0.5f)
    path.lineTo(center.x + heartWidth * 0.5f, center.y)
    path.close()
    
    drawPath(
        path = path,
        color = color
    )
}