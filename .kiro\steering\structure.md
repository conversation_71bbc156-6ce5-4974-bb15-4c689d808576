# Project Structure

## Root Level
```
├── app/                    # Main application module
├── gradle/                 # Gradle wrapper and version catalog
├── .gradle/               # Gradle cache (generated)
├── .idea/                 # Android Studio project files
├── .kiro/                 # Kiro AI assistant configuration
├── build.gradle.kts       # Root build configuration
├── settings.gradle.kts    # Project settings and modules
├── gradle.properties      # Gradle properties
├── gradlew & gradlew.bat  # Gradle wrapper scripts
└── local.properties       # Local SDK paths (not in VCS)
```

## App Module Structure
```
app/
├── src/
│   ├── main/
│   │   ├── java/          # Kotlin source code
│   │   ├── res/           # Android resources
│   │   └── AndroidManifest.xml
│   ├── test/              # Unit tests
│   └── androidTest/       # Instrumentation tests
├── build.gradle.kts       # App module build configuration
├── proguard-rules.pro     # ProGuard configuration
└── .gitignore            # App-specific ignore rules
```

## Package Organization
Based on the namespace `com.xue.love`, follow this structure:
```
src/main/java/com/xue/love/
├── ui/                    # Compose UI components
│   ├── theme/            # App theming
│   ├── screens/          # Screen composables
│   └── components/       # Reusable UI components
├── data/                 # Data layer
│   ├── repository/       # Repository pattern
│   ├── local/           # Local data sources (Room, etc.)
│   └── remote/          # Remote data sources
├── domain/               # Business logic
│   ├── model/           # Domain models
│   └── usecase/         # Use cases
├── di/                   # Dependency injection
└── MainActivity.kt       # Main activity
```

## Configuration Files
- **gradle/libs.versions.toml**: Centralized dependency version management
- **app/build.gradle.kts**: App-specific build configuration with Compose setup
- **build.gradle.kts**: Root project configuration with plugin aliases

## Conventions
- Use single-activity architecture with Compose navigation
- Follow MVVM or MVI architecture pattern
- Organize by feature when the app grows larger
- Keep resources organized by type in `res/` directories
- Use meaningful package names that reflect functionality