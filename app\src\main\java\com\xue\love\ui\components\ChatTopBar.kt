package com.xue.love.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.R

/**
 * 聊天界面增强型顶部导航栏
 * 提供快捷导航、返回按钮和状态指示
 */
@Composable
fun ChatTopBar(
    onNavigateToHome: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onBackClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
    showQuickNav: Boolean = true
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.1f),
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 左侧：返回按钮或快捷导航
            if (onBackClick != null) {
                QuickNavButton(
                    icon = R.drawable.ic_home_filled,
                    contentDescription = "返回",
                    onClick = onBackClick
                )
            } else if (showQuickNav) {
                QuickNavButton(
                    icon = R.drawable.ic_home_filled,
                    contentDescription = "主页",
                    onClick = onNavigateToHome
                )
            } else {
                Spacer(modifier = Modifier.width(40.dp))
            }
            
            // 中间：标题区域
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "私密聊天",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            // 右侧：个人中心快捷按钮
            if (showQuickNav) {
                QuickNavButton(
                    icon = R.drawable.ic_profile_filled,
                    contentDescription = "我的",
                    onClick = onNavigateToProfile
                )
            } else {
                Spacer(modifier = Modifier.width(40.dp))
            }
        }
    }
}

/**
 * 快捷导航按钮组件
 */
@Composable
private fun QuickNavButton(
    icon: Int,
    contentDescription: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1.0f,
        animationSpec = tween(150),
        label = "button_scale"
    )
    
    val backgroundColor by animateColorAsState(
        targetValue = if (isPressed) Color.White.copy(alpha = 0.3f) else Color.White.copy(alpha = 0.2f),
        animationSpec = tween(150),
        label = "background_color"
    )
    
    Box(
        modifier = modifier
            .size(40.dp)
            .scale(scale)
            .clip(CircleShape)
            .background(backgroundColor)
            .clickable(
                onClick = onClick,
                onClickLabel = contentDescription
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(icon),
            contentDescription = contentDescription,
            tint = Color.White,
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * 浮动快捷导航栏（可选方案）
 */
@Composable
fun FloatingQuickNav(
    onNavigateToHome: () -> Unit,
    onNavigateToProfile: () -> Unit,
    modifier: Modifier = Modifier,
    isVisible: Boolean = true
) {
    if (!isVisible) return
    
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(25.dp))
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFFFF6B9D).copy(alpha = 0.9f),
                        Color(0xFFC44569).copy(alpha = 0.9f)
                    )
                )
            )
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        QuickNavButton(
            icon = R.drawable.ic_home_filled,
            contentDescription = "主页",
            onClick = onNavigateToHome
        )
        
        QuickNavButton(
            icon = R.drawable.ic_profile_filled,
            contentDescription = "我的",
            onClick = onNavigateToProfile
        )
    }
}
