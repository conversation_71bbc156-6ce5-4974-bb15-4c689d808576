package com.xue.love.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.xue.love.R

/**
 * 导航设置组件
 * 允许用户自定义导航行为和外观
 */
@Composable
fun NavigationSettings(
    enableSwipeGestures: Boolean,
    onSwipeGesturesChanged: (Boolean) -> Unit,
    enableQuickNav: <PERSON><PERSON>an,
    onQuickNavChanged: (Boolean) -> Unit,
    enableFloatingNav: Boolean,
    onFloatingNavChanged: (Boolean) -> Unit,
    autoHideFloatingNav: Boolean,
    onAutoHideChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    RomanticCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "导航设置",
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = "自定义您的导航体验，让操作更加便捷",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
            
            Divider(
                color = Color(0xFFFF6B9D).copy(alpha = 0.2f),
                thickness = 1.dp
            )
            
            // 手势导航设置
            NavigationSettingItem(
                title = "手势导航",
                subtitle = "左右滑动快速切换页面",
                icon = R.drawable.ic_chat_filled,
                isEnabled = enableSwipeGestures,
                onToggle = onSwipeGesturesChanged
            )
            
            // 快捷导航设置
            NavigationSettingItem(
                title = "顶部快捷导航",
                subtitle = "在聊天界面显示快捷导航按钮",
                icon = R.drawable.ic_home_filled,
                isEnabled = enableQuickNav,
                onToggle = onQuickNavChanged
            )
            
            // 悬浮导航设置
            NavigationSettingItem(
                title = "悬浮导航球",
                subtitle = "智能悬浮导航，随时快速切换",
                icon = R.drawable.ic_heart_filled,
                isEnabled = enableFloatingNav,
                onToggle = onFloatingNavChanged
            )
            
            // 自动隐藏设置（仅在悬浮导航开启时显示）
            if (enableFloatingNav) {
                NavigationSettingItem(
                    title = "自动隐藏悬浮球",
                    subtitle = "无操作时自动隐藏悬浮导航球",
                    icon = R.drawable.ic_vibration,
                    isEnabled = autoHideFloatingNav,
                    onToggle = onAutoHideChanged,
                    modifier = Modifier.padding(start = 16.dp)
                )
            }
        }
    }
}

/**
 * 导航设置项组件
 */
@Composable
private fun NavigationSettingItem(
    title: String,
    subtitle: String,
    icon: Int,
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isEnabled) Color(0xFFFF6B9D).copy(alpha = 0.1f) else Color.Transparent,
        animationSpec = tween(300),
        label = "background_color"
    )
    
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 图标
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(
                    if (isEnabled) Color(0xFFFF6B9D).copy(alpha = 0.2f) 
                    else Color.Gray.copy(alpha = 0.1f)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(icon),
                contentDescription = title,
                tint = if (isEnabled) Color(0xFFFF6B9D) else Color.Gray,
                modifier = Modifier.size(20.dp)
            )
        }
        
        // 文本内容
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
        
        // 开关
        Switch(
            checked = isEnabled,
            onCheckedChange = onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = Color(0xFFFF6B9D),
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = Color.Gray.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * 导航预览组件
 * 显示当前导航设置的效果预览
 */
@Composable
fun NavigationPreview(
    enableSwipeGestures: Boolean,
    enableQuickNav: Boolean,
    enableFloatingNav: Boolean,
    modifier: Modifier = Modifier
) {
    RomanticCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "预览效果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
            
            // 功能状态指示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                FeatureChip(
                    text = "手势导航",
                    isEnabled = enableSwipeGestures
                )
                
                FeatureChip(
                    text = "快捷按钮",
                    isEnabled = enableQuickNav
                )
                
                FeatureChip(
                    text = "悬浮球",
                    isEnabled = enableFloatingNav
                )
            }
            
            Text(
                text = when {
                    enableSwipeGestures && enableQuickNav && enableFloatingNav -> 
                        "✨ 完整导航体验：支持手势滑动、快捷按钮和悬浮导航"
                    enableSwipeGestures && enableQuickNav -> 
                        "🚀 高效导航：支持手势滑动和快捷按钮"
                    enableSwipeGestures -> 
                        "👆 手势导航：左右滑动切换页面"
                    enableQuickNav -> 
                        "⚡ 快捷导航：顶部快捷按钮"
                    enableFloatingNav -> 
                        "🎯 悬浮导航：智能悬浮导航球"
                    else -> 
                        "📱 基础导航：仅底部导航栏"
                },
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.8f)
            )
        }
    }
}

/**
 * 功能标签组件
 */
@Composable
private fun FeatureChip(
    text: String,
    isEnabled: Boolean,
    modifier: Modifier = Modifier
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isEnabled) Color(0xFFFF6B9D) else Color.Gray.copy(alpha = 0.3f),
        animationSpec = tween(300),
        label = "chip_background"
    )
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(backgroundColor)
            .padding(horizontal = 12.dp, vertical = 6.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = if (isEnabled) Color.White else Color.Gray,
            fontWeight = FontWeight.Medium
        )
    }
}
