package com.xue.love.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 主题管理器
 * 负责管理应用的主题状态、切换和情侣同步功能
 */
class ThemeManager {
    
    // 当前主题类型
    private val _currentTheme = MutableStateFlow(CoupleThemeType.SWEET_PINK)
    val currentTheme: StateFlow<CoupleThemeType> = _currentTheme.asStateFlow()
    
    // 是否启用深色模式
    private val _isDarkMode = MutableStateFlow(false)
    val isDarkMode: StateFlow<Boolean> = _isDarkMode.asStateFlow()
    
    // 是否启用情侣主题同步
    private val _isCoupleSync = MutableStateFlow(false)
    val isCoupleSync: StateFlow<Boolean> = _isCoupleSync.asStateFlow()
    
    // 是否启用动态主题
    private val _isDynamicTheme = MutableStateFlow(false)
    val isDynamicTheme: StateFlow<Boolean> = _isDynamicTheme.asStateFlow()
    
    /**
     * 切换主题
     */
    fun switchTheme(themeType: CoupleThemeType) {
        _currentTheme.value = themeType
        // TODO: 如果启用了情侣同步，需要同步给伴侣
        if (_isCoupleSync.value) {
            syncThemeToPartner(themeType)
        }
    }
    
    /**
     * 切换深色模式
     */
    fun toggleDarkMode() {
        _isDarkMode.value = !_isDarkMode.value
        // TODO: 如果启用了情侣同步，需要同步给伴侣
        if (_isCoupleSync.value) {
            syncDarkModeToPartner(_isDarkMode.value)
        }
    }
    
    /**
     * 设置深色模式
     */
    fun setDarkMode(isDark: Boolean) {
        _isDarkMode.value = isDark
        if (_isCoupleSync.value) {
            syncDarkModeToPartner(isDark)
        }
    }
    
    /**
     * 切换情侣主题同步
     */
    fun toggleCoupleSync() {
        _isCoupleSync.value = !_isCoupleSync.value
        // TODO: 实现同步逻辑
    }
    
    /**
     * 切换动态主题
     */
    fun toggleDynamicTheme() {
        _isDynamicTheme.value = !_isDynamicTheme.value
    }
    
    /**
     * 获取当前主题的渐变色
     */
    fun getCurrentThemeGradient(): Brush {
        return when (_currentTheme.value) {
            CoupleThemeType.SWEET_PINK -> Brush.linearGradient(
                colors = listOf(
                    SweetPinkGradientStart,
                    SweetPinkGradientEnd
                )
            )
            CoupleThemeType.PASSION_RED -> Brush.linearGradient(
                colors = listOf(
                    PassionRedGradientStart,
                    PassionRedGradientEnd
                )
            )
            CoupleThemeType.MYSTERY_PURPLE -> Brush.linearGradient(
                colors = listOf(
                    MysteryPurpleGradientStart,
                    MysteryPurpleGradientEnd
                )
            )
            CoupleThemeType.ELEGANT_BLACK -> Brush.linearGradient(
                colors = listOf(
                    ElegantBlackGradientStart,
                    ElegantBlackGradientEnd
                )
            )
        }
    }
    
    /**
     * 获取当前主题的浅色渐变
     */
    fun getCurrentThemeLightGradient(): Brush {
        return when (_currentTheme.value) {
            CoupleThemeType.SWEET_PINK -> Brush.linearGradient(
                colors = listOf(
                    SweetPinkGradientLight,
                    SweetPink80
                )
            )
            CoupleThemeType.PASSION_RED -> Brush.linearGradient(
                colors = listOf(
                    PassionRedGradientLight,
                    PassionRed80
                )
            )
            CoupleThemeType.MYSTERY_PURPLE -> Brush.linearGradient(
                colors = listOf(
                    MysteryPurpleGradientLight,
                    MysteryPurple80
                )
            )
            CoupleThemeType.ELEGANT_BLACK -> Brush.linearGradient(
                colors = listOf(
                    ElegantBlackGradientLight,
                    ElegantGrey80
                )
            )
        }
    }
    
    /**
     * 获取当前主题的透明度颜色
     */
    fun getCurrentThemeAlphaColor(alpha: Float): Color {
        return when (_currentTheme.value) {
            CoupleThemeType.SWEET_PINK -> when {
                alpha >= 0.8f -> SweetPink60Alpha80
                alpha >= 0.5f -> SweetPink60Alpha50
                alpha >= 0.3f -> SweetPink60Alpha30
                else -> SweetPink60Alpha10
            }
            CoupleThemeType.PASSION_RED -> when {
                alpha >= 0.8f -> PassionRed60Alpha80
                alpha >= 0.5f -> PassionRed60Alpha50
                alpha >= 0.3f -> PassionRed60Alpha30
                else -> PassionRed60Alpha10
            }
            CoupleThemeType.MYSTERY_PURPLE -> when {
                alpha >= 0.8f -> MysteryPurple60Alpha80
                alpha >= 0.5f -> MysteryPurple60Alpha50
                alpha >= 0.3f -> MysteryPurple60Alpha30
                else -> MysteryPurple60Alpha10
            }
            CoupleThemeType.ELEGANT_BLACK -> when {
                alpha >= 0.8f -> ElegantGrey60Alpha80
                alpha >= 0.5f -> ElegantGrey60Alpha50
                alpha >= 0.3f -> ElegantGrey60Alpha30
                else -> ElegantGrey60Alpha10
            }
        }
    }
    
    /**
     * 获取主题显示名称
     */
    fun getThemeDisplayName(themeType: CoupleThemeType): String {
        return when (themeType) {
            CoupleThemeType.SWEET_PINK -> "甜蜜粉"
            CoupleThemeType.PASSION_RED -> "激情红"
            CoupleThemeType.MYSTERY_PURPLE -> "神秘紫"
            CoupleThemeType.ELEGANT_BLACK -> "优雅黑"
        }
    }
    
    /**
     * 获取主题描述
     */
    fun getThemeDescription(themeType: CoupleThemeType): String {
        return when (themeType) {
            CoupleThemeType.SWEET_PINK -> "温柔浪漫的粉色调，营造甜蜜氛围"
            CoupleThemeType.PASSION_RED -> "热情奔放的红色调，点燃爱的激情"
            CoupleThemeType.MYSTERY_PURPLE -> "神秘优雅的紫色调，增添浪漫情调"
            CoupleThemeType.ELEGANT_BLACK -> "简约时尚的黑色调，展现优雅品味"
        }
    }
    
    // TODO: 实现与伴侣的主题同步
    private fun syncThemeToPartner(themeType: CoupleThemeType) {
        // 实现主题同步逻辑
    }
    
    private fun syncDarkModeToPartner(isDark: Boolean) {
        // 实现深色模式同步逻辑
    }
    
    companion object {
        @Volatile
        private var INSTANCE: ThemeManager? = null
        
        fun getInstance(): ThemeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ThemeManager().also { INSTANCE = it }
            }
        }
    }
}

/**
 * Composable函数，用于在UI中获取主题管理器状态
 */
@Composable
fun rememberThemeManager(): ThemeManager {
    return ThemeManager.getInstance()
}

/**
 * Composable函数，用于获取当前主题状态
 */
@Composable
fun rememberCurrentTheme(): CoupleThemeType {
    val themeManager = rememberThemeManager()
    val currentTheme by themeManager.currentTheme.collectAsState()
    return currentTheme
}

/**
 * Composable函数，用于获取当前深色模式状态
 */
@Composable
fun rememberIsDarkMode(): Boolean {
    val themeManager = rememberThemeManager()
    val isDarkMode by themeManager.isDarkMode.collectAsState()
    return isDarkMode
}