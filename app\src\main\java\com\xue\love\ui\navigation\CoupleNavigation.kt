package com.xue.love.ui.navigation

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.xue.love.ui.screens.chat.ChatScreen
import com.xue.love.ui.screens.home.HomeScreen
import com.xue.love.ui.screens.profile.ProfileScreen

/**
 * 情侣APP导航管理器
 * 提供流畅的页面切换动画
 */
@Composable
fun CoupleNavigation(
    selectedTab: Int,
    paddingValues: PaddingValues,
    modifier: Modifier = Modifier
) {
    AnimatedContent(
        targetState = selectedTab,
        modifier = modifier.padding(paddingValues),
        transitionSpec = {
            // 根据导航方向选择不同的动画
            val direction = if (targetState > initialState) 1 else -1
            
            slideInHorizontally(
                initialOffsetX = { fullWidth -> direction * fullWidth },
                animationSpec = tween(300)
            ) + fadeIn(
                animationSpec = tween(300)
            ) togetherWith slideOutHorizontally(
                targetOffsetX = { fullWidth -> -direction * fullWidth },
                animationSpec = tween(300)
            ) + fadeOut(
                animationSpec = tween(300)
            )
        },
        label = "navigation_animation"
    ) { tabIndex ->
        when (tabIndex) {
            0 -> HomeScreen()
            1 -> ChatScreen()
            2 -> ProfileScreen()
        }
    }
}

/**
 * 导航目标枚举
 */
enum class NavigationDestination(val index: Int, val title: String) {
    HOME(0, "主页"),
    CHAT(1, "聊天"),
    PROFILE(2, "我")
}

/**
 * 导航状态管理
 */
data class NavigationState(
    val currentDestination: NavigationDestination = NavigationDestination.HOME,
    val previousDestination: NavigationDestination? = null
) {
    fun navigateTo(destination: NavigationDestination): NavigationState {
        return copy(
            currentDestination = destination,
            previousDestination = currentDestination
        )
    }
}