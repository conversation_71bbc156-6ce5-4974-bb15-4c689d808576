package com.xue.love.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.unit.dp
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart
import kotlin.math.cos
import kotlin.math.sin

/**
 * 导航指示器组件
 * 提供脉冲和渐变效果的页面指示器
 */
@Composable
fun NavigationIndicator(
    selectedIndex: Int,
    totalCount: Int,
    modifier: Modifier = Modifier
) {
    val animatedPosition by animateFloatAsState(
        targetValue = selectedIndex.toFloat(),
        animationSpec = tween(300),
        label = "indicator_position"
    )
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(4.dp)
            .padding(horizontal = 32.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        Canvas(
            modifier = Modifier.fillMaxWidth()
        ) {
            drawNavigationIndicator(
                animatedPosition = animatedPosition,
                totalCount = totalCount,
                drawScope = this
            )
        }
    }
}

/**
 * 绘制导航指示器
 */
private fun drawNavigationIndicator(
    animatedPosition: Float,
    totalCount: Int,
    drawScope: DrawScope
) {
    val width = drawScope.size.width
    val height = drawScope.size.height
    val indicatorWidth = width / totalCount
    val indicatorHeight = height
    
    // 绘制背景指示器
    for (i in 0 until totalCount) {
        val startX = i * indicatorWidth
        drawScope.drawRect(
            color = Pink60.copy(alpha = 0.2f),
            topLeft = Offset(startX, 0f),
            size = androidx.compose.ui.geometry.Size(indicatorWidth * 0.8f, indicatorHeight)
        )
    }
    
    // 绘制活动指示器
    val activeStartX = animatedPosition * indicatorWidth
    val gradientBrush = Brush.horizontalGradient(
        colors = listOf(PinkGradientStart, PinkGradientEnd),
        startX = activeStartX,
        endX = activeStartX + indicatorWidth * 0.8f
    )
    
    drawScope.drawRect(
        brush = gradientBrush,
        topLeft = Offset(activeStartX, 0f),
        size = androidx.compose.ui.geometry.Size(indicatorWidth * 0.8f, indicatorHeight)
    )
}

/**
 * 圆形脉冲指示器
 */
@Composable
fun PulsingCircleIndicator(
    isActive: Boolean,
    modifier: Modifier = Modifier,
    activeColor: Color = Pink60,
    inactiveColor: Color = Pink60.copy(alpha = 0.3f)
) {
    val scale by animateFloatAsState(
        targetValue = if (isActive) 1.2f else 1.0f,
        animationSpec = tween(300),
        label = "circle_scale"
    )
    
    val alpha by animateFloatAsState(
        targetValue = if (isActive) 1.0f else 0.5f,
        animationSpec = tween(300),
        label = "circle_alpha"
    )
    
    Canvas(
        modifier = modifier
    ) {
        val center = Offset(size.width / 2, size.height / 2)
        val radius = (size.minDimension / 2) * scale
        
        // 绘制脉冲圆圈
        if (isActive) {
            for (i in 1..3) {
                val pulseRadius = radius * (1 + i * 0.3f)
                val pulseAlpha = alpha / (i * 2)
                drawCircle(
                    color = activeColor.copy(alpha = pulseAlpha),
                    radius = pulseRadius,
                    center = center
                )
            }
        }
        
        // 绘制主圆圈
        drawCircle(
            color = if (isActive) activeColor else inactiveColor,
            radius = radius,
            center = center
        )
    }
}

/**
 * 爱心形状的导航指示器
 */
@Composable
fun HeartShapeIndicator(
    isActive: Boolean,
    modifier: Modifier = Modifier,
    activeColor: Color = Pink60,
    inactiveColor: Color = Pink60.copy(alpha = 0.3f)
) {
    val scale by animateFloatAsState(
        targetValue = if (isActive) 1.3f else 1.0f,
        animationSpec = tween(300),
        label = "heart_scale"
    )
    
    Canvas(
        modifier = modifier
    ) {
        val center = Offset(size.width / 2, size.height / 2)
        val heartSize = (size.minDimension / 3) * scale
        
        drawHeartShape(
            center = center,
            size = heartSize,
            color = if (isActive) activeColor else inactiveColor
        )
    }
}

/**
 * 绘制爱心形状
 */
private fun DrawScope.drawHeartShape(
    center: Offset,
    size: Float,
    color: Color
) {
    val path = androidx.compose.ui.graphics.Path()
    
    // 爱心的数学公式
    val steps = 100
    for (i in 0..steps) {
        val t = (i.toFloat() / steps) * 2 * Math.PI
        val x = 16 * sin(t).pow(3)
        val y = -(13 * cos(t) - 5 * cos(2 * t) - 2 * cos(3 * t) - cos(4 * t))
        
        val scaledX = center.x + (x * size / 16).toFloat()
        val scaledY = center.y + (y * size / 16).toFloat()
        
        if (i == 0) {
            path.moveTo(scaledX, scaledY)
        } else {
            path.lineTo(scaledX, scaledY)
        }
    }
    
    path.close()
    
    drawPath(
        path = path,
        color = color
    )
}

private fun Double.pow(n: Int): Double {
    var result = 1.0
    repeat(n) {
        result *= this
    }
    return result
}