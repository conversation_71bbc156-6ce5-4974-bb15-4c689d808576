package com.xue.love.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.theme.CoupleAppTheme
import kotlin.math.cos
import kotlin.math.sin

/**
 * 爱心形状的进度条
 */
@Composable
fun HeartProgressBar(
    progress: Float,
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color(0xFFFF6B9D),
        Color(0xFFC44569)
    ),
    backgroundColor: Color = Color(0xFFFFE0E6)
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress.coerceIn(0f, 1f),
        animationSpec = tween(1000),
        label = "heartProgress"
    )
    
    Canvas(
        modifier = modifier
            .size(120.dp)
    ) {
        drawHeartProgress(
            progress = animatedProgress,
            colors = colors,
            backgroundColor = backgroundColor,
            size = size
        )
    }
}

/**
 * 圆形亲密度仪表盘
 */
@Composable
fun IntimacyMeter(
    intimacyLevel: Float,
    maxLevel: Float = 100f,
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color(0xFFE91E63),
        Color(0xFFFF5722)
    )
) {
    val animatedLevel by animateFloatAsState(
        targetValue = intimacyLevel.coerceIn(0f, maxLevel),
        animationSpec = tween(1500),
        label = "intimacyLevel"
    )
    
    val progress = animatedLevel / maxLevel
    
    Box(
        modifier = modifier.size(150.dp),
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier.size(150.dp)
        ) {
            drawCircularProgress(
                progress = progress,
                colors = colors,
                strokeWidth = 12.dp.toPx(),
                size = size
            )
        }
        
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                contentDescription = "亲密度",
                tint = colors.first(),
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "${animatedLevel.toInt()}",
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = colors.first()
            )
            
            Text(
                text = "亲密度",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 浪漫风格的线性进度条
 */
@Composable
fun RomanticLinearProgress(
    progress: Float,
    label: String,
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color(0xFFFF6B9D),
        Color(0xFFC44569)
    ),
    backgroundColor: Color = Color(0xFFFFE0E6)
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress.coerceIn(0f, 1f),
        animationSpec = tween(1000),
        label = "linearProgress"
    )
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Text(
                text = "${(animatedProgress * 100).toInt()}%",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = colors.first()
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(backgroundColor)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(animatedProgress)
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(
                        brush = Brush.horizontalGradient(colors)
                    )
            )
        }
    }
}

/**
 * 绘制爱心形状的进度
 */
private fun DrawScope.drawHeartProgress(
    progress: Float,
    colors: List<Color>,
    backgroundColor: Color,
    size: Size
) {
    val center = Offset(size.width / 2, size.height / 2)
    val heartSize = size.minDimension * 0.8f
    
    // 绘制背景爱心
    drawHeartShape(
        center = center,
        size = heartSize,
        color = backgroundColor,
        filled = true
    )
    
    // 绘制进度爱心
    if (progress > 0f) {
        drawHeartShape(
            center = center,
            size = heartSize,
            brush = Brush.linearGradient(colors),
            filled = true,
            progress = progress
        )
    }
}

/**
 * 绘制圆形进度
 */
private fun DrawScope.drawCircularProgress(
    progress: Float,
    colors: List<Color>,
    strokeWidth: Float,
    size: Size
) {
    val center = Offset(size.width / 2, size.height / 2)
    val radius = (size.minDimension - strokeWidth) / 2
    
    // 绘制背景圆环
    drawCircle(
        color = Color(0xFFFFE0E6),
        radius = radius,
        center = center,
        style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
    )
    
    // 绘制进度圆环
    if (progress > 0f) {
        drawArc(
            brush = Brush.sweepGradient(colors, center),
            startAngle = -90f,
            sweepAngle = 360f * progress,
            useCenter = false,
            topLeft = Offset(center.x - radius, center.y - radius),
            size = Size(radius * 2, radius * 2),
            style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
        )
    }
}

/**
 * 绘制爱心形状
 */
private fun DrawScope.drawHeartShape(
    center: Offset,
    size: Float,
    color: Color? = null,
    brush: Brush? = null,
    filled: Boolean = true,
    progress: Float = 1f
) {
    val heartPath = androidx.compose.ui.graphics.Path()
    val scale = size / 32f
    
    // 爱心的数学公式路径
    val steps = (100 * progress).toInt()
    var firstPoint = true
    
    for (i in 0..steps) {
        val t = i * 2 * Math.PI / 100
        val x = 16 * sin(t) * sin(t) * sin(t)
        val y = -(13 * cos(t) - 5 * cos(2 * t) - 2 * cos(3 * t) - cos(4 * t))
        
        val scaledX = center.x + (x * scale).toFloat()
        val scaledY = center.y + (y * scale).toFloat()
        
        if (firstPoint) {
            heartPath.moveTo(scaledX, scaledY)
            firstPoint = false
        } else {
            heartPath.lineTo(scaledX, scaledY)
        }
    }
    
    if (progress >= 1f) {
        heartPath.close()
    }
    
    when {
        color != null -> drawPath(heartPath, color)
        brush != null -> drawPath(heartPath, brush)
    }
}

@Preview(showBackground = true)
@Composable
fun RomanticProgressPreview() {
    CoupleAppTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            HeartProgressBar(progress = 0.75f)
            
            IntimacyMeter(intimacyLevel = 85f)
            
            RomanticLinearProgress(
                progress = 0.6f,
                label = "今日互动"
            )
        }
    }
}