package com.xue.love.ui.screens.pairing

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.components.HeartParticleEffect
import com.xue.love.ui.components.RomanticButton
import com.xue.love.ui.components.RomanticCard
import com.xue.love.ui.theme.CoupleAppTheme

/**
 * 精美的配对界面
 * 包含爱心背景动画、优雅的二维码扫描界面和温馨的引导文案
 */
@Composable
fun PairingScreen(
    modifier: Modifier = Modifier,
    onPairingCodeEntered: (String) -> Unit = {},
    onScanQRCode: () -> Unit = {},
    onGenerateCode: () -> Unit = {}
) {
    var pairingCode by remember { mutableStateOf("") }
    
    // 爱心旋转动画
    val infiniteTransition = rememberInfiniteTransition(label = "heartRotation")
    val heartRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 20000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "heartRotation"
    )
    
    // 脉冲动画
    val heartScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "heartScale"
    )
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 渐变背景
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color(0xFFFF6B9D).copy(alpha = 0.3f),
                            Color(0xFF9C27B0).copy(alpha = 0.2f),
                            Color(0xFF673AB7).copy(alpha = 0.1f)
                        ),
                        radius = 1000f
                    )
                )
        )
        
        // 爱心粒子效果
        HeartParticleEffect(
            particleCount = 25,
            colors = listOf(
                Color(0xFFFF6B9D).copy(alpha = 0.6f),
                Color(0xFFFFE0E6).copy(alpha = 0.4f),
                Color(0xFFC44569).copy(alpha = 0.5f)
            )
        )
        
        // 背景装饰爱心
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            contentAlignment = Alignment.TopEnd
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                contentDescription = null,
                tint = Color(0xFFFFE0E6).copy(alpha = 0.3f),
                modifier = Modifier
                    .size(120.dp)
                    .rotate(heartRotation)
                    .scale(heartScale)
            )
        }
        
        // 主要内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // 标题区域
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 主图标
                Box(
                    modifier = Modifier
                        .size(120.dp)
                        .clip(CircleShape)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color.White.copy(alpha = 0.2f),
                                    Color.White.copy(alpha = 0.1f)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
                        contentDescription = "情侣配对",
                        modifier = Modifier.size(60.dp),
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "爱恋配对",
                    style = MaterialTheme.typography.displayMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "与你的爱人建立专属的私密连接\n开启我们的浪漫之旅",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.White.copy(alpha = 0.9f),
                    textAlign = TextAlign.Center,
                    lineHeight = MaterialTheme.typography.bodyLarge.lineHeight * 1.2
                )
            }
            
            Spacer(modifier = Modifier.height(48.dp))
            
            // 配对选项卡片
            RomanticCard(
                colors = listOf(
                    Color.White.copy(alpha = 0.95f),
                    Color.White.copy(alpha = 0.9f)
                ),
                borderColors = listOf(
                    Color(0xFFFF6B9D).copy(alpha = 0.3f),
                    Color(0xFF9C27B0).copy(alpha = 0.3f)
                )
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    Text(
                        text = "选择配对方式",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2D1B20),
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center
                    )
                    
                    // 输入配对码
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "输入配对码",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                        )
                        
                        OutlinedTextField(
                            value = pairingCode,
                            onValueChange = { pairingCode = it },
                            modifier = Modifier.fillMaxWidth(),
                            placeholder = {
                                Text(
                                    text = "请输入你爱人分享的配对码",
                                    color = Color(0xFF2D1B20).copy(alpha = 0.5f)
                                )
                            },
                            shape = RoundedCornerShape(12.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = Color(0xFFFF6B9D),
                                unfocusedBorderColor = Color(0xFF2D1B20).copy(alpha = 0.3f),
                                focusedTextColor = Color(0xFF2D1B20),
                                unfocusedTextColor = Color(0xFF2D1B20)
                            ),
                            singleLine = true
                        )
                        
                        RomanticButton(
                            text = "开始配对",
                            onClick = { onPairingCodeEntered(pairingCode) },
                            modifier = Modifier.fillMaxWidth(),
                            colors = listOf(
                                Color(0xFFFF6B9D),
                                Color(0xFFC44569)
                            ),
                            enabled = pairingCode.isNotBlank()
                        )
                    }
                    
                    // 分割线
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(1.dp)
                                .background(Color(0xFF2D1B20).copy(alpha = 0.2f))
                        )
                        
                        Text(
                            text = "或者",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF2D1B20).copy(alpha = 0.6f)
                        )
                        
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .height(1.dp)
                                .background(Color(0xFF2D1B20).copy(alpha = 0.2f))
                        )
                    }
                    
                    // 其他配对方式
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        RomanticButton(
                            text = "扫描二维码",
                            onClick = onScanQRCode,
                            modifier = Modifier.weight(1f),
                            colors = listOf(
                                Color(0xFF9C27B0),
                                Color(0xFF673AB7)
                            ),
                            icon = ImageVector.vectorResource(R.drawable.ic_chat_filled)
                        )
                        
                        RomanticButton(
                            text = "生成配对码",
                            onClick = onGenerateCode,
                            modifier = Modifier.weight(1f),
                            colors = listOf(
                                Color(0xFFE91E63),
                                Color(0xFFFF5722)
                            ),
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 底部提示
            Text(
                text = "💕 配对成功后，你们将拥有专属的私密空间",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun PairingScreenPreview() {
    CoupleAppTheme {
        PairingScreen()
    }
}