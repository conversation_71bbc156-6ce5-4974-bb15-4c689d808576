package com.xue.love.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart

/**
 * 情侣APP浮动操作按钮
 * 用于快速发送爱心、震动等特殊互动
 */
@Composable
fun CoupleFloatingActionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    icon: ImageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
    contentDescription: String = "发送爱心"
) {
    var isPressed by remember { mutableStateOf(false) }
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.9f else 1.0f,
        animationSpec = tween(150),
        label = "fab_scale"
    )
    
    val gradientBrush = Brush.radialGradient(
        colors = listOf(
            PinkGradientStart,
            PinkGradientEnd
        )
    )

    Box(
        modifier = modifier.scale(scale),
        contentAlignment = Alignment.Center
    ) {
        FloatingActionButton(
            onClick = {
                isPressed = true
                onClick()
                // 重置按压状态
                isPressed = false
            },
            modifier = Modifier
                .size(64.dp)
                .clip(CircleShape)
                .background(gradientBrush),
            containerColor = Color.Transparent,
            contentColor = Color.White,
            elevation = FloatingActionButtonDefaults.elevation(
                defaultElevation = 8.dp,
                pressedElevation = 12.dp
            )
        ) {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                modifier = Modifier.size(28.dp),
                tint = Color.White
            )
        }
        
        // 添加脉冲效果的背景圆圈
        Box(
            modifier = Modifier
                .size(72.dp)
                .clip(CircleShape)
                .background(
                    Pink60.copy(alpha = 0.2f)
                )
        )
    }
}

/**
 * 带脉冲动画的浮动操作按钮
 */
@Composable
fun PulsingCoupleFloatingActionButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    icon: ImageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
    contentDescription: String = "发送爱心",
    isPulsing: Boolean = true
) {
    var pulseScale by remember { mutableStateOf(1.0f) }
    
    // 脉冲动画
    val animatedScale by animateFloatAsState(
        targetValue = if (isPulsing) pulseScale else 1.0f,
        animationSpec = tween(1000),
        finishedListener = {
            if (isPulsing) {
                pulseScale = if (pulseScale == 1.0f) 1.1f else 1.0f
            }
        },
        label = "pulse_scale"
    )
    
    Box(
        modifier = modifier.scale(animatedScale),
        contentAlignment = Alignment.Center
    ) {
        CoupleFloatingActionButton(
            onClick = onClick,
            icon = icon,
            contentDescription = contentDescription
        )
    }
}